'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/Dialog';
import { Input } from '@/components/ui/Input';
import { VisuallyHidden } from '@/components/ui/VisuallyHidden';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/Tooltip';
import type { DropzoneRootProps } from 'react-dropzone';
import Image from 'next/image';
import type { ImageState } from '@/store/imageStore';
import { useImageStore } from '@/store/imageStore';
import UploadIcon from '@/components/icons/Upload';
import { EditableImageName } from './EditableImageName';
import { formatFileSize } from '@/lib/imageUtils/imageCompress';
import DeleteIcon from '@/components/icons/Delete';
import DowloadIcon from '@/components/icons/Dowload';
import { useSimpleHistory } from '@/hooks/batch-editor/useSimpleHistory';
import { imageStorage } from '@/storage/indexeddbStorage';
import { getPasteShortcut } from '@/lib/device';
import { CreditDisplay } from '@/components/common/CreditDisplay';
import {
  downloadSingleImage,
  getImageDownloadInfo,
} from '@/lib/imageUtils/imageDownload';
import type { SupportedFormat } from '@/lib/imageUtils/imageConvert';
import { useTranslations } from 'next-intl';
import { MAX_BATCH_IMAGES_LIMIT } from '@/config/constants';
import { useTips } from '@/components/ui/Tips';

interface ImageUploadGridProps {
  getRootProps: () => DropzoneRootProps;
  open: () => void;
  isDragActive: boolean;
  handleLoadFromUrl: (url: string) => Promise<void>;
  imagesCount: number;
  images?: ImageState[];
  processingImageIds?: Set<string>;
}

export function ImageUploadGrid({
  getRootProps,
  open,
  isDragActive,
  handleLoadFromUrl,
  imagesCount,
  images = [],
  processingImageIds = new Set(),
}: ImageUploadGridProps) {
  const common = useTranslations('common');
  const messages = useTranslations('messages');
  const singleImage = useTranslations('singleImage');
  const [isUrlDialogOpen, setIsUrlDialogOpen] = useState(false);
  const [inputUrl, setInputUrl] = useState('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);

  // 删除确认弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [imageToDelete, setImageToDelete] = useState<string | null>(null);

  // 历史记录功能
  const { canUndo, canRedo, undo, redo, clearHistory } = useSimpleHistory();
  const { showTips } = useTips();

  const handleUrlSubmit = async () => {
    if (!inputUrl.trim()) return;

    setIsLoadingUrl(true);
    try {
      await handleLoadFromUrl(inputUrl);
      setIsUrlDialogOpen(false);
      setInputUrl('');
    } finally {
      setIsLoadingUrl(false);
    }
  };

  // 显示删除确认弹窗（删除所有图片）
  const showDeleteConfirmation = () => {
    setImageToDelete(null); // null 表示删除所有图片
    setDeleteDialogOpen(true);
  };

  // 显示单个图片删除确认弹窗
  const showDeleteImageConfirmation = (imageId: string) => {
    setImageToDelete(imageId);
    setDeleteDialogOpen(true);
  };

  // 取消删除
  const cancelDelete = () => {
    setDeleteDialogOpen(false);
    setImageToDelete(null);
  };

  // 确认删除（根据imageToDelete判断是删除单个还是全部）
  const confirmDelete = async () => {
    setIsDeleting(true);
    try {
      if (imageToDelete) {
        // 删除单个图片
        useImageStore.getState().removeImage(imageToDelete);
      } else {
        // 删除所有图片
        // 1. 清空所有图片
        useImageStore.getState().clearImages();

        // 2. 清空历史记录
        clearHistory();

        // 3. 清空 IndexedDB
        await imageStorage.safeClearCurrentSession();
      }
    } catch (error) {
      console.error('删除失败:', error);
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setImageToDelete(null);
    }
  };

  // 下载单个图片
  const handleDownloadImage = async (image: ImageState) => {
    try {
      const downloadInfo = await getImageDownloadInfo(image);
      await downloadSingleImage(
        downloadInfo.url,
        downloadInfo.fileName,
        downloadInfo.format as SupportedFormat
      );
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  // 渲染上传卡片
  const renderUploadCard = () => {
    // 如果达到图片数量限制，直接返回null，不渲染上传卡片
    if (imagesCount >= MAX_BATCH_IMAGES_LIMIT) {
      return null;
    }

    return (
      <div
        {...getRootProps()}
        className={`w-[222px] h-[222px] border border-[#E7E7E7] rounded-2xl flex flex-col items-center justify-center cursor-pointer transition-colors interactive-container hover:border-2 hover:text-brand-primary hover:border-primary`}
        style={{
          boxShadow:
            '0px 7px 14px 0px rgba(220, 223, 228, 0.16), 0px 8px 16px 0px rgba(220, 223, 228, 0.12), 0px 10px 32px 0px rgba(220, 223, 228, 0.08)',
        }}
        onClick={e => {
          e.stopPropagation();
          open();
        }}
      >
        <div className='mb-2 flex items-center justify-center'>
          {/* 导出图标 */}
          <UploadIcon className='icon-interactive size-12' />
        </div>
        <span className='text-base font-normal'>{common('uploadImage')}</span>
      </div>
    );
  };

  // 渲染图片卡片
  const renderImageCard = (image: ImageState) => (
    <div key={image.id} className={`w-[222px] transition-all`}>
      {/* 图片容器 */}
      <div
        className='w-[222px] h-[222px] border border-[#E7E7E7] rounded-2xl overflow-hidden relative group'
        style={{
          // 如果有合成预览URL，使用白色背景；否则使用设置的背景
          backgroundColor: image.compositePreviewUrl
            ? '#ffffff'
            : image.backgroundImageUrl
              ? 'transparent'
              : image.backgroundColor || '#ffffff',
          backgroundImage: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? `url(${image.backgroundImageUrl})`
              : undefined,
          backgroundSize: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? 'cover'
              : undefined,
          backgroundPosition: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? 'center'
              : undefined,
          backgroundRepeat: image.compositePreviewUrl
            ? undefined
            : image.backgroundImageUrl
              ? 'no-repeat'
              : undefined,
          boxShadow:
            '0px 6.216px 12.432px 0px rgba(220, 223, 228, 0.16), 0px 7.104px 14.208px 0px rgba(220, 223, 228, 0.12), 0px 8.88px 28.416px 0px rgba(220, 223, 228, 0.08)',
        }}
      >
        <Image
          src={
            image.compositePreviewUrl ||
            image.resizedUrl ||
            image.processedUrl ||
            image.previewUrl
          }
          alt={image.name}
          fill
          className={`object-cover ${
            image.compositePreviewUrl || image.resizedUrl || image.processedUrl
              ? 'object-contain'
              : 'object-cover'
          }`}
          draggable={false}
        />

        {/* 统一处理中状态 */}
        {processingImageIds.has(image.id) && (
          <div className='absolute inset-0 flex items-center justify-center bg-[rgba(18,18,18,0.10)] backdrop-blur-[4px]'>
            <div className='w-12 h-12 flex items-center justify-center'>
              {/* 锁图标 */}
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={48}
                height={48}
                className='animate-spin'
              />
            </div>
          </div>
        )}

        {/* 锁定状态 - 非会员限制 */}
        {image.status === 'locked' && !processingImageIds.has(image.id) && (
          <div className='absolute inset-0 flex items-center justify-center bg-[rgba(18,18,18,0.10)] backdrop-blur-[4px]'>
            <div className='w-12 h-12 flex items-center justify-center'>
              {/* 锁图标 */}
              <Image
                src='/apps/icons/lock.svg'
                alt='lock'
                width={48}
                height={48}
              />
            </div>
          </div>
        )}

        {/* 背景去除失败状态 */}
        {image.status === 'bg-remove-failed' &&
          !processingImageIds.has(image.id) && (
            <div className='absolute inset-0 rounded-2xl bg-[rgba(18,18,18,0.10)] backdrop-blur-[4px] flex flex-col items-center justify-center gap-3'>
              {/* 警告图标 */}
              <div className='w-16 h-16 rounded-full flex items-center justify-center'>
                <Image
                  src='/apps/icons/dialogWarning.svg'
                  alt='dialogWarning'
                  width={64}
                  height={64}
                />
              </div>

              {/* 错误文本 */}
              <p
                className='text-white text-center text-base font-normal px-4 leading-6'
                style={{ textShadow: '0px 2px 4px rgba(0, 0, 0, 0.50)' }}
              >
                {messages('batchEditor.sorryCouldntRemoveBatch')}
              </p>

              {/* Keep 按钮 */}
              <Button
                onClick={e => {
                  e.stopPropagation();
                  // 恢复为 original 状态
                  useImageStore.getState().updateImage(image.id, {
                    status: 'original',
                  });
                }}
                className='w-30 h-10  text-base font-medium rounded-lg border-0 px-6'
              >
                {common('keep')}
              </Button>
            </div>
          )}

        {/* 下载按钮 - hover时显示 */}
        <Button
          variant='ghost'
          size='icon'
          className='interactive-container absolute top-2 left-2 w-[28px] h-[28px] rounded-lg p-1 bg-[#686868] hover:bg-[#686868] opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer'
          onClick={e => {
            e.stopPropagation();
            handleDownloadImage(image);
          }}
        >
          <DowloadIcon className='icon-interactive-white w-4 h-4' />
        </Button>

        {/* 删除按钮 - hover时显示 */}
        <Button
          variant='ghost'
          size='icon'
          className='interactive-container absolute top-2 right-2 w-[28px] h-[28px] rounded-lg p-1 bg-[#686868] hover:bg-[#686868] opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer'
          onClick={e => {
            e.stopPropagation();
            showDeleteImageConfirmation(image.id);
          }}
        >
          <DeleteIcon className='icon-interactive-white w-4 h-4' />
        </Button>

        {/* 如果有处理后的图片且不在处理中且不是锁定状态，显示成功图标 */}
      </div>

      {/* 图片信息 */}
      <div className='mt-2 text-left px-2'>
        <div className='w-[206px]'>
          <EditableImageName
            name={image.name}
            onNameChange={newName => {
              useImageStore.getState().updateImageName(image.id, newName);
            }}
            className='w-full'
          />
        </div>
        <div className='text-[#878787] text-sm space-y-1'>
          {/* 显示尺寸变化或原始尺寸 */}
          <div>
            {image.targetWidth && image.targetHeight ? (
              <div className='flex items-center justify-start gap-1'>
                <span>
                  {image.originalWidth || image.width}x
                  {image.originalHeight || image.height}
                </span>
                <span>→</span>
                <span
                  className='px-2 py-1 rounded text-sm font-medium'
                  style={{ backgroundColor: '#D7EFCE', color: '#121212' }}
                >
                  {image.targetWidth}x{image.targetHeight}
                </span>
              </div>
            ) : (
              <span>
                {image.width}x{image.height}
              </span>
            )}
          </div>

          {/* 显示文件大小变化 */}
          {(() => {
            // 如果文件大小发生了变化（调整尺寸、压缩等操作），显示原始大小 → 当前大小
            if (
              image.originalSize &&
              image.size !== image.originalSize &&
              (image.targetWidth ||
                image.targetHeight ||
                image.compressedSize ||
                image.convertedFormat)
            ) {
              return (
                <div className='flex items-center justify-start gap-1'>
                  <span>{formatFileSize(image.originalSize)}</span>
                  <span>→</span>
                  <span
                    className='px-2 py-1 rounded text-sm font-medium'
                    style={{ backgroundColor: '#D7EFCE', color: '#2D5A3D' }}
                  >
                    {formatFileSize(image.size)}
                  </span>
                </div>
              );
            }

            // 默认显示当前文件大小
            return image.size > 0 && <span>{formatFileSize(image.size)}</span>;
          })()}
        </div>
      </div>
    </div>
  );

  return (
    <div className='flex-1 bg-bg-light flex flex-col px-8 py-6'>
      {/* 主要上传区域 */}
      {images.length > 0 ? (
        <div>
          {/* 底部导航栏 */}
          <div className='flex justify-between items-center pb-6'>
            {/* 左侧导航按钮 */}
            <div className='flex items-center gap-2'>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='w-8 h-8 p-0 m-0 hover:bg-[#F0F0F0]'
                    onClick={() => undo()}
                    disabled={!canUndo}
                    style={{ opacity: canUndo ? 1 : 0.3 }}
                  >
                    <Image
                      src='/apps/icons/previous.svg'
                      alt='undo'
                      width={24}
                      height={24}
                    />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{common('undo')}</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='w-8 h-8 p-0 m-0 hover:bg-[#F0F0F0]'
                    onClick={() => redo()}
                    disabled={!canRedo}
                    style={{ opacity: canRedo ? 1 : 0.3 }}
                  >
                    <Image
                      src='/apps/icons/next.svg'
                      alt='redo'
                      width={24}
                      height={24}
                    />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{common('redo')}</TooltipContent>
              </Tooltip>
              {/* 中间积分显示 */}
              <CreditDisplay />
            </div>

            {/* 右侧计数和删除按钮 */}
            <div className='flex items-center gap-4'>
              <div className='bg-[rgba(255,204,3,0.1)] rounded-full px-4 py-1'>
                <span className='text-[#FF781F] text-base font-medium'>
                  {images.length} / {MAX_BATCH_IMAGES_LIMIT}
                </span>
              </div>
              {/* 删除按钮 */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant='ghost'
                    size='icon'
                    className='interactive-container cursor-pointer w-8 h-8'
                    onClick={e => {
                      e.stopPropagation();
                      showDeleteConfirmation();
                    }}
                    disabled={isDeleting}
                  >
                    <DeleteIcon className='icon-interactive size-6' />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{common('deleteAll')}</TooltipContent>
              </Tooltip>
            </div>
          </div>
          {/* 主要内容区域 */}
          <div className='flex-1'>
            {/* 网格容器 */}
            <div className='grid gap-3 grid-cols-[repeat(auto-fill,222px)] auto-rows-max'>
              {/* 第一个位置始终是上传卡片 */}
              {renderUploadCard()}

              {/* 显示已上传的图片 */}
              {images.map(renderImageCard)}
            </div>
          </div>
        </div>
      ) : (
        <div className='bg-white w-full h-full rounded-3xl shadow-[0px_10px_32px_0px_rgba(220,223,228,0.08),0px_8px_16px_0px_rgba(220,223,228,0.12),0px_7px_14px_0px_rgba(220,223,228,0.16)] p-4'>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-2xl h-full flex flex-col items-center justify-center relative ${
              isDragActive ? 'border-blue-500 bg-blue-50' : 'border-[#d0d0d0]'
            }`}
          >
            {/* 装饰性图片图标 */}
            <div className='relative mb-4'>
              {/* 上传图标 */}
              <Image
                src='/apps/batch_upload_icon.png'
                alt='add'
                width={200}
                height={200}
              />
            </div>
            <div className='text-center'>
              <p className='text-text-primary text-2xl font-bold mb-2 leading-tight'>
                {singleImage('initial.dragAndDrop')}
              </p>
              <p className='text-text-secondary text-base mb-4 leading-relaxed'>
                {singleImage('initial.pasteImage').replace(
                  '(Ctrl +V)',
                  `(${getPasteShortcut()})`
                )}{' '}
                {common('or')}{' '}
                <Dialog
                  open={isUrlDialogOpen}
                  onOpenChange={setIsUrlDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant='link'
                      className='p-0 h-auto text-base text-[#ffcc03] hover:text-[#ffdb4d] underline'
                      onClick={e => e.stopPropagation()}
                    >
                      URL
                    </Button>
                  </DialogTrigger>
                  <DialogContent className='w-[542px] bg-white rounded-2xl shadow-[0px_4px_8px_0px_rgba(19,19,20,0.5)] border-0 p-0'>
                    <VisuallyHidden>
                      <DialogTitle>
                        {singleImage('initial.pasteImageUrl')}
                      </DialogTitle>
                    </VisuallyHidden>
                    {/* 顶部关闭按钮区域 */}
                    <div className='h-10 relative rounded-t-[6px] w-full border-b border-[#e7e7e7]'></div>

                    {/* 主要内容区域 */}
                    <div className='px-8 py-6'>
                      <div className='flex flex-col gap-6'>
                        {/* 标题和输入框 */}
                        <div className='flex flex-col gap-3'>
                          <h3 className='text-[#121212] text-[16px] font-medium  leading-[1.5]'>
                            {singleImage('initial.pasteImageUrl')}
                          </h3>
                          <div className='relative'>
                            <Input
                              placeholder={singleImage(
                                'initial.pleaseInputImageUrl'
                              )}
                              value={inputUrl}
                              onChange={e => setInputUrl(e.target.value)}
                              onKeyDown={e => {
                                if (e.key === 'Enter') {
                                  handleUrlSubmit();
                                }
                              }}
                              disabled={isLoadingUrl}
                              className='h-10 bg-[#f9fafb] border-[#e7e7e7] rounded-lg px-3 py-[9px] text-[14px]  placeholder:text-[#b8b8b8] focus:ring-2 focus:ring-[#ffcc03] focus:border-[#ffcc03]'
                            />
                          </div>
                        </div>

                        {/* 按钮区域 */}
                        <div className='flex justify-end gap-3'>
                          <Button
                            variant='outline'
                            onClick={() => {
                              setIsUrlDialogOpen(false);
                              setInputUrl('');
                            }}
                            disabled={isLoadingUrl}
                            className='h-10 w-24 rounded-lg border-[#e7e7e7] bg-white text-[#000000] text-[16px] font-medium  hover:bg-gray-50'
                          >
                            {common('cancel')}
                          </Button>
                          <Button
                            onClick={handleUrlSubmit}
                            disabled={!inputUrl.trim() || isLoadingUrl}
                            className='h-10 w-24 rounded-lg bg-[#ffcc03] text-[#000000] text-[16px] font-medium  hover:bg-[#ffcc03]/90 border-0'
                          >
                            {isLoadingUrl ? (
                              <Image
                                src='/apps/icons/loading.png'
                                alt='loading'
                                width={16}
                                height={16}
                                className='animate-spin'
                              />
                            ) : (
                              common('confirm')
                            )}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </p>

              <Button
                className='h-12 px-6 w-66 cursor-pointer'
                onClick={e => {
                  e.stopPropagation();
                  if (imagesCount >= MAX_BATCH_IMAGES_LIMIT) {
                    showTips(
                      'error',
                      messages('singleImage.imagesExceedLimit', {
                        count: MAX_BATCH_IMAGES_LIMIT,
                      })
                    );
                    return;
                  }
                  open();
                }}
              >
                <Image
                  src='/apps/icons/add.svg'
                  alt='add'
                  width={24}
                  height={24}
                />
                <span>{common('selectImage')}</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认弹窗 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className='w-[542px] bg-white rounded-2xl shadow-[0px_4px_8px_0px_rgba(19,19,20,0.5)] border-0 p-0'>
          <VisuallyHidden>
            <DialogTitle>{messages('singleImage.confirmDelete')}</DialogTitle>
          </VisuallyHidden>
          {/* 顶部关闭按钮区域 */}
          <div className='h-10 relative rounded-t-2xl w-full border-b border-[#e7e7e7]'></div>

          {/* 主要内容区域 */}
          <div className='px-8 pb-8'>
            <div className='w-full flex flex-col gap-6'>
              <div className='flex items-center justify-start gap-5'>
                {/* 问号图标 */}
                <div>
                  <Image
                    src='/apps/icons/dialogInfo.svg'
                    alt='dialogInfo'
                    width={64}
                    height={64}
                  />
                </div>
                <div>
                  {/* 标题 */}
                  <p className='text-[#121212] text-[18px] font-medium leading-[1.5] text-center'>
                    {imageToDelete
                      ? messages('singleImage.confirmDelete')
                      : messages('batchEditor.confirmDeleteAll')}
                  </p>
                </div>
              </div>
              {/* 按钮区域 */}
              <div className='flex gap-3 w-full justify-end'>
                <Button
                  variant='outline'
                  onClick={cancelDelete}
                  disabled={isDeleting}
                  className='border-[#e7e7e7] bg-white hover:bg-gray-50'
                >
                  {common('cancel')}
                </Button>
                <Button
                  onClick={confirmDelete}
                  disabled={isDeleting}
                  className='bg-red-500 hover:bg-red-600 text-white'
                >
                  {isDeleting ? (
                    <Image
                      src='/apps/icons/loading.png'
                      alt='loading'
                      width={16}
                      height={16}
                      className='animate-spin'
                    />
                  ) : imageToDelete ? (
                    common('delete')
                  ) : (
                    common('deleteAll')
                  )}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
