{
  "common": {
    "cancel": "Cancelar",
    "confirm": "Confirmar",
    "delete": "Eliminar",
    "upload": "Subir",
    "download": "Descargar",
    "apply": "Aplicar",
    "processing": "Procesando",
    "free": "<PERSON><PERSON><PERSON>",
    "unlock": "Desbloquear",
    "previousStep": "Paso anterior",
    "nextStep": "Siguiente paso",
    "compareOriginal": "Comparar imagen original",
    "deleteAll": "Eliminar todo",
    "keep": "Mantener",
    "undo": "<PERSON>hacer",
    "redo": "<PERSON>hacer",
    "reset": "Restablecer",
    "or": "o",
    "url": "URL",
    "uploadImage": "Subir imagen",
    "selectImage": "Seleccionar imagen",
    "previous": "Anterior",
    "next": "Siguiente",
    "morePages": "Más páginas",
    "loading": "cargando...",
    "noMoreData": "No hay más datos",
    "noDownloadableImages": "No hay imágenes descargables",
    "downloadFailed": "Error en la descarga, por favor inténtelo de nuevo"
  },
  "auth": {
    "signIn": "Iniciar sesión",
    "signUp": "Regístrate",
    "myAccount": "Mi cuenta",
    "logout": "Cerrar sesión"
  },
  "singleImage": {
    "initial": {
      "removeBackground": "Eliminar fondo",
      "uploadImageToRemove": "Sube una imagen para eliminar el fondo",
      "dragAndDrop": "Arrastra y suelta tu imagen aquí",
      "uploadImage": "Subir imagen",
      "pasteImage": "Pega una imagen{shortcut} o introduce una <url>URL</url>"
      "supportedFormats": "Formatos compatibles:",
      "noImageTryThese": "¿No hay imagen? Prueba con una de estas:",
      "recaptchaNotice": "Este sitio está protegido por reCAPTCHA y se aplican la Política de privacidad y los Términos de servicio de Google.",
      "pasteImageUrl": "Pegar URL de la imagen",
      "pleaseInputImageUrl": "Por favor, introduzca la URL de la imagen"
    },
    "interface": {
      "changeBackgroundColors": "Cambiar colores de fondo",
      "changeBackgroundPhotos": "Cambiar fotos de fondo",
      "eraseRestore": "Borrar / Restaurar",
      "blurBackground": "Desenfocar fondo",
      "previewSize": "Vista previa",
      "maxQualitySize": "Calidad máxima"
    },
    "backgroundColors": {
      "customColor": "Color personalizado",
      "presetColor": "Color preestablecido"
    },
    "backgroundPhotos": {
      "gradient": "Degradado",
      "landscape": "paisaje",
      "geometric": "Geométrico",
      "wood": "Madera",
      "paper": "Papel",
      "texture": "Textura"
    },
    "eraseRestore": {
      "brushSize": "Tamaño del pincel",
      "zoomOut": "Alejar",
      "zoomIn": "Acercar",
      "grip": "Agarre",
      "handTool": "Herramienta manual - Haz clic para arrastrar",
      "title": "Borrar / Restaurar",
      "erase": "Borrar",
      "restore": "Restaurar",
      "deactivateHandTool": "Desactivar herramienta manual"
    },
    "backgroundBlur": {
      "enableBackgroundBlur": "Activar desenfoque de fondo",
      "blurAmount": "Nivel de desenfoque"
    },
    "shadow": {
      "opacity": "Opacidad"
    }
  },
  "batchEditor": {
    "interface": {
      "batchEditor": "Editor por lotes",
      "background": "Fondo",
      "resize": "Redimensionar",
      "rename": "Renombrar",
      "convert": "Convertir",
      "compress": "Comprimir"
    },
    "background": {
      "color": "Color",
      "photos": "Fotos"
    },
    "resize": {
      "marketplacePlatforms": "Para plataformas de mercado",
      "tiktokShop": "Tienda TikTok",
      "amazon": "Amazon",
      "ebay": "eBay",
      "postmark": "Postmark",
      "depop": "Depop",
      "mercari": "Mercari",
      "mercadoLibre": "Mercado Libre",
      "shopee": "Shopee",
      "shopifySquare": "Shopify Square",
      "shopifyLandscape": "Panorama de Shopify",
      "shopifyPortrait": "Retrato de Shopify",
      "lazada": "Lazada",
      "etsy": "Etsy",
      "vinted": "Vinted",
      "socialMediaPlatforms": "Para plataformas de redes sociales",
      "instagramStory": "Historia de Instagram",
      "instagramPost": "Publicación de Instagram",
      "facebookCover": "Portada de Facebook",
      "facebookPost": "Publicación de Facebook",
      "facebookMarketplace": "Facebook Marketplace",
      "tiktokPost": "Publicación de TikTok",
      "tiktokCover": "Portada de TikTok",
      "youtubeCover": "Portada de YouTube",
      "youtubeChannel": "Canal de YouTube",
      "twitterCover": "Portada de Twitter",
      "twitterPost": "Publicación de Twitter",
      "ratioSize": "Tamaño de proporción",
      "square": "Cuadrado (1:1)",
      "passportId": "Foto de pasaporte y documento de identidad (2x2 pulgadas)",
      "customSize": "Tamaño personalizado",
      "byScale": "Por escala",
      "byDimensions": "Por dimensiones",
      "width": "Ancho",
      "height": "Altura",
      "lockAspectRatio": "Bloquear relación de aspecto",
      "unlockAspectRatio": "Desbloquear relación de aspecto",
      "pixelPx": "Píxel (px)",
      "inchIn": "Pulgada (in)",
      "millimeterMm": "Milímetro (mm)",
      "originalSize": "Tamaño original",
      "selectCategory": "Seleccionar una categoría",
      "ratio": "Proporción",
      "custom": "Personalizado"
    },
    "rename": {
      "prefix": "Prefijo",
      "pleaseEnterName": "Por favor, ingrese un nombre",
      "startNumber": "Número de inicio",
      "numberStep": "Paso numérico"
    },
    "compress": {
      "compressionLevel": "Nivel de compresión",
      "original": "Original",
      "lightBestClarity": "Claro (máxima claridad)",
      "mediumBalanced": "Media (Calidad equilibrada)",
      "highSmallest": "Alta (tamaño más pequeño)",
      "eachImageUnder": "Debajo de cada imagen",
      "enterNumberOnly": "Ingrese solo un número",
      "supportedRange": "Rango compatible"
    }
  },
  "messages": {
    "singleImage": {
      "unableToOpenImage": "No se puede abrir la imagen. La URL no es válida o es incorrecta.",
      "imagesExceedLimit": "El número de imágenes supera el límite. Se pueden subir un máximo de {count} imágenes.",
      "confirmDelete": "¿Está seguro de que desea eliminar?",
      "sorryCouldntRemove": "Lo sentimos, no pudimos eliminar el fondo. Intenta cepillarlo con la herramienta Pincel.",
      "tryBrush": "Probar el pincel",
      "imageUploadFailed": "Error al subir la imagen.",
      "unsupportedImageFormat": "Formato de imagen no compatible.",
      "tryBrushSuggestion": "Intenta cepillarlo con la herramienta Pincel."
    },
    "batchEditor": {
      "confirmDeleteAll": "¿Está seguro de que desea eliminar todas las imágenes?",
      "sorryCouldntRemoveBatch": "Lo sentimos, no pudimos eliminar el fondo.",
      "allProcessedSuccessfully": "Todo se procesó correctamente",
      "someImagesFailed": "{failed} de {total} imágenes no se pudieron procesar",
      "batchFailed": "Error en el procesamiento por lotes",
      "sizeTooSmall": "Tamaño demasiado pequeño. El valor mínimo es de 100 px (≈ 1 pulgada o 26 mm).",
      "sizeTooLarge": "Tamaño demasiado grande. El valor máximo es de 4000 px (≈ 42 pulgadas o 1058 mm).",
      "pleaseEnterValidFileSize": "Por favor, introduzca un tamaño de archivo válido.",
      "fileSizeMinimum": "El tamaño del archivo debe ser de al menos 20 KB.",
      "fileSizeMaximum": "El tamaño del archivo no puede superar los 10 MB."
    }
  },
  "account": {
    "credits": "Créditos",
    "myAccount": "Mi cuenta",
    "goToTenorshareAccount": "Ir al Centro de Cuenta de Tenorshare",
    "timeFrame": "Intervalo de tiempo",
    "past30Days": "en los últimos 30 días",
    "past14Days": "en los últimos 14 días",
    "past7Days": "en los últimos 7 días",
    "timeZone": "Zona horaria",
    "transactionHistory": "Historial de transacciones",
    "usageChart": "Gráfico de uso",
    "date": "Fecha",
    "time": "Hora",
    "amount": "Importe",
    "description": "Descripción",
    "bonusCredits": "Créditos de bonificación",
    "subscriptionCredits": "Créditos de suscripción",
    "bonusExpired": "El bono ha caducado",
    "subscriptionExpired": "La suscripción ha expirado",
    "removeBG": "Eliminar fondo",
    "batchEditorRemoveBG": "Editor por lotes Eliminar fondo",
    "receivedFreeCredits": "Has recibido <count>5</count> créditos de IA gratis hoy.",
    "usedAllFreeCredits": "Has utilizado los 5 créditos gratis de hoy. Mejora tu plan para obtener más créditos →",
    "advancedAI": "IA avanzada",
    "advancedAIUsage": "<count>3</count><separator> de </separator><total>5</total> créditos gratuitos usados",
    "advancedAIEnabled": "IA avanzada activada · Ver uso →",
    "monthlyLimitReached": "Has alcanzado tu límite de uso mensual. Actualiza para continuar →",
    "pickDate": "Seleccionar fecha"
  },
  "mobile": {
    "selectPhotos": "Por favor, selecciona entre 1 y 10 fotos",
    "upload": "Subir",
    "select": "Seleccionar",
    "selected": "Seleccionado",
    "allowAccessPhotos": "Permitir acceso a tus fotos",
    "accessPhotosDescription": "Para subir y editar tus fotos, necesitamos acceso a tu biblioteca de fotos. Solo accederemos a las fotos que selecciones.",
    "notNow": "Ahora no",
    "allowAccess": "Permitir acceso",
    "accessDenied": "Acceso denegado",
    "accessDeniedDescription": "Ha denegado el acceso a su biblioteca de fotos. Para subir imágenes, permita el acceso en la configuración de su dispositivo.",
    "goToSettings": "Ir a Configuración",
    "bgColors": "Colores de fondo",
    "bgPhotos": "Fotos de fondo",
    "eraseRestore": "Borrar / Restaurar",
    "blurBG": "Desenfocar fondo"
  },
  "error": {
    "title": "Algo salió mal",
    "message": "La aplicación encontró un error inesperado. Por favor, intente actualizar la página.",
    "viewDetails": "Ver detalles del error",
    "refresh": "Actualizar página"
  },
  "metadata": {
    "title": "Herramienta de eliminación de fondo con IA - Procesamiento inteligente de fondo de imagen",
    "description": "Utilice tecnología de IA avanzada para eliminar fondos de imágenes con un clic. Admite reemplazo de fondo, ajuste de color, adición de sombras y otras funciones de edición profesional."
  }
}
