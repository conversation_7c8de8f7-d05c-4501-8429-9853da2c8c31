{
  "common": {
    "cancel": "İptal",
    "confirm": "<PERSON><PERSON><PERSON>",
    "delete": "Sil",
    "upload": "<PERSON><PERSON><PERSON>",
    "download": "<PERSON><PERSON><PERSON>",
    "apply": "Uygula",
    "processing": "İşleniyor",
    "free": "<PERSON>cretsiz",
    "unlock": "Kilidi Aç",
    "previousStep": "Önceki adım",
    "nextStep": "Sonraki adım",
    "compareOriginal": "Orijinal resmi karşılaştır",
    "deleteAll": "Tümünü sil",
    "keep": "<PERSON><PERSON><PERSON>",
    "undo": "Geri Al",
    "redo": "Yinele",
    "reset": "Sıfırla",
    "or": "veya",
    "url": "URL",
    "uploadImage": "Görsel Yükle",
    "selectImage": "<PERSON><PERSON>rse<PERSON> seç",
    "previous": "Önce<PERSON>",
    "next": "<PERSON>rak<PERSON>",
    "morePages": "<PERSON>ha fazla sayfa",
    "loading": "yükleniyor...",
    "noMoreData": "<PERSON>ha fazla veri yok",
    "noDownloadableImages": "İndirilebilir görsel yok",
    "downloadFailed": "İndirme başarısız, lütfen tekrar deneyin"
  },
  "auth": {
    "signIn": "Giriş yap",
    "signUp": "Kayıt Ol",
    "myAccount": "Hesabım",
    "logout": "Oturumu Kapat"
  },
  "singleImage": {
    "initial": {
      "removeBackground": "Arka planı kaldır",
      "uploadImageToRemove": "Arka planı kaldırmak için görsel yükleyin",
      "dragAndDrop": "Görüntünüzü buraya sürükleyip bırakın",
      "uploadImage": "Görsel Yükle",
      "pasteImage": "Görüntü yapıştırın{shortcut} veya <url>URL</url> girin"
      "supportedFormats": "Desteklenen formatlar:",
      "noImageTryThese": "Resim yok mu? Bunlardan birini deneyin:",
      "recaptchaNotice": "Bu site reCAPTCHA ile korunmaktadır ve Google Gizlilik Politikası ile Hizmet Şartları geçerlidir.",
      "pasteImageUrl": "Görsel URL'sini yapıştır",
      "pleaseInputImageUrl": "Lütfen görsel URL'sini girin"
    },
    "interface": {
      "changeBackgroundColors": "Arka plan renklerini değiştir",
      "changeBackgroundPhotos": "Arka plan fotoğraflarını değiştir",
      "eraseRestore": "Sil / Geri Yükle",
      "blurBackground": "Arka planı bulanıklaştır",
      "previewSize": "Önizleme",
      "maxQualitySize": "Maksimum Kalite"
    },
    "backgroundColors": {
      "customColor": "Özel Renk",
      "presetColor": "Ön ayarlı renk"
    },
    "backgroundPhotos": {
      "gradient": "Gradyan",
      "landscape": "Manzara",
      "geometric": "Geometrik",
      "wood": "Ahşap",
      "paper": "Kağıt",
      "texture": "Doku"
    },
    "eraseRestore": {
      "brushSize": "Fırça Boyutu",
      "zoomOut": "Uzaklaştır",
      "zoomIn": "Yakınlaştır",
      "grip": "Tutuş",
      "handTool": "El aracı - Sürüklemek için tıklayın",
      "title": "Sil / Geri Yükle",
      "erase": "Sil",
      "restore": "Geri Yükle",
      "deactivateHandTool": "El aracını devre dışı bırak"
    },
    "backgroundBlur": {
      "enableBackgroundBlur": "Arka plan bulanıklığını etkinleştir",
      "blurAmount": "Bulanıklık miktarı"
    },
    "shadow": {
      "opacity": "Opaklık"
    }
  },
  "batchEditor": {
    "interface": {
      "batchEditor": "Toplu Düzenleyici",
      "background": "Arka plan",
      "resize": "Yeniden boyutlandır",
      "rename": "Yeniden Adlandır",
      "convert": "Dönüştür",
      "compress": "Sıkıştır"
    },
    "background": {
      "color": "Renk",
      "photos": "Fotoğraflar"
    },
    "resize": {
      "marketplacePlatforms": "Pazar yeri platformları için",
      "tiktokShop": "TikTok Mağazası",
      "amazon": "Amazon",
      "ebay": "eBay",
      "postmark": "Postmark",
      "depop": "Depop",
      "mercari": "Mercari",
      "mercadoLibre": "Mercado Libre",
      "shopee": "Shopee",
      "shopifySquare": "Shopify Square",
      "shopifyLandscape": "Shopify manzarası",
      "shopifyPortrait": "Shopify portresi",
      "lazada": "Lazada",
      "etsy": "Etsy",
      "vinted": "Vinted",
      "socialMediaPlatforms": "Sosyal medya platformları için",
      "instagramStory": "Instagram hikayesi",
      "instagramPost": "Instagram gönderisi",
      "facebookCover": "Facebook Kapak Fotoğrafı",
      "facebookPost": "Facebook gönderisi",
      "facebookMarketplace": "Facebook Marketplace",
      "tiktokPost": "TikTok gönderisi",
      "tiktokCover": "TikTok Kapağı",
      "youtubeCover": "YouTube Kapak Resmi",
      "youtubeChannel": "YouTube kanalı",
      "twitterCover": "Twitter Kapak",
      "twitterPost": "Twitter gönderisi",
      "ratioSize": "Oran Boyutu",
      "square": "Kare (1:1)",
      "passportId": "Pasaport ve Kimlik Fotoğrafı (2x2 inç)",
      "customSize": "Özel Boyut",
      "byScale": "Ölçeğe göre",
      "byDimensions": "Boyutlara göre",
      "width": "Genişlik",
      "height": "Yükseklik",
      "lockAspectRatio": "En-boy oranını kilitle",
      "unlockAspectRatio": "En-boy oranının kilidini aç",
      "pixelPx": "Piksel (px)",
      "inchIn": "İnç (in)",
      "millimeterMm": "Milimetre (mm)",
      "originalSize": "Orijinal Boyut",
      "selectCategory": "Bir kategori seçin",
      "ratio": "Oran",
      "custom": "Özel"
    },
    "rename": {
      "prefix": "Önek",
      "pleaseEnterName": "Lütfen bir isim girin",
      "startNumber": "Başlangıç Numarası",
      "numberStep": "Sayı Adımı"
    },
    "compress": {
      "compressionLevel": "Sıkıştırma Seviyesi",
      "original": "Orijinal",
      "lightBestClarity": "Açık (En İyi Netlik)",
      "mediumBalanced": "Orta (Dengeli Kalite)",
      "highSmallest": "Yüksek (En Küçük Boyut)",
      "eachImageUnder": "Her görselin altında",
      "enterNumberOnly": "Yalnızca bir sayı girin",
      "supportedRange": "Desteklenen aralık"
    }
  },
  "messages": {
    "singleImage": {
      "unableToOpenImage": "Resim açılamıyor. URL geçersiz veya hatalı.",
      "imagesExceedLimit": "Görsel sayısı sınırı aşıyor. En fazla {count} görsel yükleyebilirsiniz.",
      "confirmDelete": "Silmek istediğinizden emin misiniz?",
      "sorryCouldntRemove": "Üzgünüz, arka plan kaldırılamadı. Fırça aracıyla üzerinden geçmeyi deneyin.",
      "tryBrush": "Fırçayı Dene",
      "imageUploadFailed": "Görsel yüklenemedi.",
      "unsupportedImageFormat": "Desteklenmeyen resim formatı.",
      "tryBrushSuggestion": "Fırça aracıyla üzerinden geçmeyi deneyin."
    },
    "batchEditor": {
      "confirmDeleteAll": "Tüm görselleri silmek istediğinizden emin misiniz?",
      "sorryCouldntRemoveBatch": "Üzgünüz, arka plan kaldırılamadı.",
      "allProcessedSuccessfully": "Hepsi başarıyla işlendi",
      "someImagesFailed": "{total} görselin {failed}'ü işlenemedi",
      "batchFailed": "Toplu işlem başarısız oldu",
      "sizeTooSmall": "Boyut çok küçük. Minimum değer 100 px (≈ 1 inç veya 26 mm).",
      "sizeTooLarge": "Boyut çok büyük. Maksimum değer 4000 piksel'dir (≈ 42 inç veya 1058 mm).",
      "pleaseEnterValidFileSize": "Lütfen geçerli bir dosya boyutu girin.",
      "fileSizeMinimum": "Dosya boyutu en az 20 KB olmalıdır.",
      "fileSizeMaximum": "Dosya boyutu 10 MB'yi geçemez."
    }
  },
  "account": {
    "credits": "Katkılar",
    "myAccount": "Hesabım",
    "goToTenorshareAccount": "Tenorshare Hesap Merkezine Git",
    "timeFrame": "Zaman aralığı",
    "past30Days": "son 30 gün içinde",
    "past14Days": "son 14 gün içinde",
    "past7Days": "son 7 gün içinde",
    "timeZone": "Zaman dilimi",
    "transactionHistory": "İşlem Geçmişi",
    "usageChart": "Kullanım Grafiği",
    "date": "Tarih",
    "time": "Zaman",
    "amount": "Tutar",
    "description": "Açıklama",
    "bonusCredits": "Bonus Kredileri",
    "subscriptionCredits": "Abonelik Kredileri",
    "bonusExpired": "Bonusun süresi doldu",
    "subscriptionExpired": "Abonelik süresi doldu",
    "removeBG": "Arka planı kaldır",
    "batchEditorRemoveBG": "Toplu Düzenleyici Arka Planı Kaldır",
    "receivedFreeCredits": "Bugün <count>5</count> ücretsiz yapay zeka kredisi aldınız.",
    "usedAllFreeCredits": "Bugünkü 5 ücretsiz hakkınızı kullandınız. Daha fazla hak için yükseltin →",
    "advancedAI": "Gelişmiş Yapay Zeka",
    "advancedAIUsage": "<total>5</total><separator> ücretsiz krediden </separator><count>3</count>'ü kullanıldı",
    "advancedAIEnabled": "Gelişmiş Yapay Zeka etkin · Kullanımı görüntüle →",
    "monthlyLimitReached": "Aylık kullanım sınırınıza ulaştınız. Devam etmek için yükseltin →",
    "pickDate": "Tarih seçin"
  },
  "mobile": {
    "selectPhotos": "Lütfen 1–10 fotoğraf seçin",
    "upload": "Yükle",
    "select": "Seç",
    "selected": "Seçildi",
    "allowAccessPhotos": "Fotoğraflarınıza erişime izin verin",
    "accessPhotosDescription": "Fotoğraflarınızı yüklemek ve düzenlemek için fotoğraf kitaplığınıza erişmemiz gerekiyor. Yalnızca seçtiğiniz fotoğraflara erişeceğiz.",
    "notNow": "Şimdi değil",
    "allowAccess": "Erişime izin ver",
    "accessDenied": "Erişim reddedildi",
    "accessDeniedDescription": "Fotoğraf kitaplığınıza erişimi reddettiniz. Görüntü yüklemek için lütfen cihaz ayarlarından erişime izin verin.",
    "goToSettings": "Ayarlar'a git",
    "bgColors": "Arka Plan Renkleri",
    "bgPhotos": "Arka plan fotoğrafları",
    "eraseRestore": "Sil / Geri Yükle",
    "blurBG": "Arka planı bulanıklaştır"
  },
  "error": {
    "title": "Bir şeyler yanlış gitti",
    "message": "Uygulama beklenmeyen bir hatayla karşılaştı. Lütfen sayfayı yenilemeyi deneyin.",
    "viewDetails": "Hata ayrıntılarını görüntüle",
    "refresh": "Sayfayı yenile"
  },
  "metadata": {
    "title": "AI Arka Plan Kaldırma Aracı - Akıllı Görsel Arka Plan İşleme",
    "description": "Gelişmiş AI teknolojisini kullanarak tek tıkla görsel arka planlarını kaldırın. Arka plan değiştirme, renk ayarlama, gölge ekleme ve diğer profesyonel düzenleme işlevlerini destekler."
  }
}
