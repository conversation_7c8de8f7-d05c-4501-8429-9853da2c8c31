{
  "common": {
    "cancel": "Abbrechen",
    "confirm": "Bestätigen",
    "delete": "Löschen",
    "upload": "Hochladen",
    "download": "Herunterladen",
    "apply": "Anwenden",
    "processing": "Wird verarbeitet",
    "free": "<PERSON><PERSON><PERSON>",
    "unlock": "Entsperren",
    "previousStep": "Vorheriger Schritt",
    "nextStep": "Nächster Schritt",
    "compareOriginal": "Originalbild vergleichen",
    "deleteAll": "Alles löschen",
    "keep": "Beibehalten",
    "undo": "Rückgängig",
    "redo": "Wiederholen",
    "reset": "Zurücksetzen",
    "or": "oder",
    "url": "URL",
    "uploadImage": "Bild hochladen",
    "selectImage": "Bild auswählen",
    "previous": "Vorherige",
    "next": "Nächste",
    "morePages": "Weitere Seiten",
    "loading": "lädt...",
    "noMoreData": "Keine weiteren Daten",
    "noDownloadableImages": "Keine herunterladbaren Bilder",
    "downloadFailed": "Download fehlgeschlagen, bitte versuchen Sie es erneut"
  },
  "auth": {
    "signIn": "Anmelden",
    "signUp": "Registrieren",
    "myAccount": "Mein Konto",
    "logout": "Abmelden"
  },
  "singleImage": {
    "initial": {
      "removeBackground": "Hintergrund entfernen",
      "uploadImageToRemove": "Bild hochladen, um den Hintergrund zu entfernen",
      "dragAndDrop": "Ziehen Sie Ihr Bild hierher",
      "uploadImage": "Bild hochladen",
      "pasteImage": "Bild einfügen{shortcut} oder <url>URL</url> eingeben"
      "supportedFormats": "Unterstützte Formate:",
      "noImageTryThese": "Kein Bild? Versuchen Sie eines dieser:",
      "recaptchaNotice": "Diese Website ist durch reCAPTCHA geschützt und es gelten die Datenschutzrichtlinie und Nutzungsbedingungen von Google.",
      "pasteImageUrl": "Bild-URL einfügen",
      "pleaseInputImageUrl": "Bitte Bild-URL eingeben"
    },
    "interface": {
      "changeBackgroundColors": "Hintergrundfarben ändern",
      "changeBackgroundPhotos": "Hintergrundfotos ändern",
      "eraseRestore": "Löschen / Wiederherstellen",
      "blurBackground": "Hintergrund verwischen",
      "previewSize": "Vorschau",
      "maxQualitySize": "Maximale Qualität"
    },
    "backgroundColors": {
      "customColor": "Benutzerdefinierte Farbe",
      "presetColor": "Voreingestellte Farbe"
    },
    "backgroundPhotos": {
      "gradient": "Verlauf",
      "landscape": "Landschaft",
      "geometric": "Geometrisch",
      "wood": "Holz",
      "paper": "Papier",
      "texture": "Textur"
    },
    "eraseRestore": {
      "brushSize": "Pinselgröße",
      "zoomOut": "Verkleinern",
      "zoomIn": "Vergrößern",
      "grip": "Griff",
      "handTool": "Hand-Werkzeug – Zum Ziehen klicken",
      "title": "Löschen / Wiederherstellen",
      "erase": "Löschen",
      "restore": "Wiederherstellen",
      "deactivateHandTool": "Hand-Werkzeug deaktivieren"
    },
    "backgroundBlur": {
      "enableBackgroundBlur": "Hintergrundunschärfe aktivieren",
      "blurAmount": "Unschärfegrad"
    },
    "shadow": {
      "opacity": "Deckkraft"
    }
  },
  "batchEditor": {
    "interface": {
      "batchEditor": "Stapel-Editor",
      "background": "Hintergrund",
      "resize": "Größe ändern",
      "rename": "Umbenennen",
      "convert": "Konvertieren",
      "compress": "Komprimieren"
    },
    "background": {
      "color": "Farbe",
      "photos": "Fotos"
    },
    "resize": {
      "marketplacePlatforms": "Für Marktplatzplattformen",
      "tiktokShop": "TikTok-Shop",
      "amazon": "Amazon",
      "ebay": "eBay",
      "postmark": "Postmark",
      "depop": "Depop",
      "mercari": "Mercari",
      "mercadoLibre": "Mercado Libre",
      "shopee": "Shopee",
      "shopifySquare": "Shopify Square",
      "shopifyLandscape": "Shopify-Landschaft",
      "shopifyPortrait": "Shopify-Porträt",
      "lazada": "Lazada",
      "etsy": "Etsy",
      "vinted": "Vinted",
      "socialMediaPlatforms": "Für soziale Medienplattformen",
      "instagramStory": "Instagram-Story",
      "instagramPost": "Instagram-Beitrag",
      "facebookCover": "Facebook-Titelbild",
      "facebookPost": "Facebook-Beitrag",
      "facebookMarketplace": "Facebook Marketplace",
      "tiktokPost": "TikTok-Beitrag",
      "tiktokCover": "TikTok-Titelbild",
      "youtubeCover": "YouTube-Titelbild",
      "youtubeChannel": "YouTube-Kanal",
      "twitterCover": "Twitter-Titelbild",
      "twitterPost": "Twitter-Beitrag",
      "ratioSize": "Seitenverhältnis",
      "square": "Quadrat (1:1)",
      "passportId": "Pass- und Ausweisfoto (2x2 Zoll)",
      "customSize": "Benutzerdefinierte Größe",
      "byScale": "Nach Maßstab",
      "byDimensions": "Nach Abmessungen",
      "width": "Breite",
      "height": "Höhe",
      "lockAspectRatio": "Seitenverhältnis sperren",
      "unlockAspectRatio": "Seitenverhältnis entsperren",
      "pixelPx": "Pixel (px)",
      "inchIn": "Zoll (in)",
      "millimeterMm": "Millimeter (mm)",
      "originalSize": "Originalgröße",
      "selectCategory": "Kategorie auswählen",
      "ratio": "Verhältnis",
      "custom": "Benutzerdefiniert"
    },
    "rename": {
      "prefix": "Präfix",
      "pleaseEnterName": "Bitte geben Sie einen Namen ein",
      "startNumber": "Startnummer",
      "numberStep": "Zahlenschritt"
    },
    "compress": {
      "compressionLevel": "Komprimierungsstufe",
      "original": "Original",
      "lightBestClarity": "Hell (beste Klarheit)",
      "mediumBalanced": "Mittel (Ausgewogene Qualität)",
      "highSmallest": "Hoch (kleinste Größe)",
      "eachImageUnder": "Unter jedem Bild",
      "enterNumberOnly": "Geben Sie nur eine Zahl ein",
      "supportedRange": "Unterstützter Bereich"
    }
  },
  "messages": {
    "singleImage": {
      "unableToOpenImage": "Bild kann nicht geöffnet werden. Die URL ist ungültig oder falsch.",
      "imagesExceedLimit": "Die Anzahl der Bilder überschreitet das Limit. Es können maximal {count} Bilder hochgeladen werden.",
      "confirmDelete": "Möchten Sie das wirklich löschen?",
      "sorryCouldntRemove": "Entschuldigung, der Hintergrund konnte nicht entfernt werden. Versuchen Sie, ihn mit dem Pinsel zu übermalen.",
      "tryBrush": "Pinsel ausprobieren",
      "imageUploadFailed": "Bild-Upload fehlgeschlagen.",
      "unsupportedImageFormat": "Nicht unterstütztes Bildformat.",
      "tryBrushSuggestion": "Versuchen Sie, ihn mit dem Pinsel zu übermalen."
    },
    "batchEditor": {
      "confirmDeleteAll": "Möchten Sie wirklich alle Bilder löschen?",
      "sorryCouldntRemoveBatch": "Entschuldigung, der Hintergrund konnte nicht entfernt werden.",
      "allProcessedSuccessfully": "Alle erfolgreich verarbeitet",
      "someImagesFailed": "{failed} von {total} Bildern konnten nicht verarbeitet werden",
      "batchFailed": "Stapelverarbeitung fehlgeschlagen",
      "sizeTooSmall": "Größe zu klein. Der Mindestwert beträgt 100 px (≈ 1 Zoll oder 26 mm).",
      "sizeTooLarge": "Größe zu groß. Der Maximalwert beträgt 4000 px (≈ 42 Zoll oder 1058 mm).",
      "pleaseEnterValidFileSize": "Bitte geben Sie eine gültige Dateigröße ein.",
      "fileSizeMinimum": "Die Dateigröße muss mindestens 20 KB betragen.",
      "fileSizeMaximum": "Die Dateigröße darf 10 MB nicht überschreiten."
    }
  },
  "account": {
    "credits": "Danksagungen",
    "myAccount": "Mein Konto",
    "goToTenorshareAccount": "Zum Tenorshare-Kontozentrum gehen",
    "timeFrame": "Zeitrahmen",
    "past30Days": "in den letzten 30 Tagen",
    "past14Days": "in den letzten 14 Tagen",
    "past7Days": "in den letzten 7 Tagen",
    "timeZone": "Zeitzone",
    "transactionHistory": "Transaktionsverlauf",
    "usageChart": "Nutzungsdiagramm",
    "date": "Datum",
    "time": "Zeit",
    "amount": "Betrag",
    "description": "Beschreibung",
    "bonusCredits": "Bonuspunkte",
    "subscriptionCredits": "Abonnement-Guthaben",
    "bonusExpired": "Bonus abgelaufen",
    "subscriptionExpired": "Abonnement abgelaufen",
    "removeBG": "Hintergrund entfernen",
    "batchEditorRemoveBG": "Stapel-Editor Hintergrund entfernen",
    "receivedFreeCredits": "Sie haben heute <count>5</count> kostenlose KI-Gutschriften erhalten.",
    "usedAllFreeCredits": "Sie haben alle 5 kostenlosen Guthaben für heute verwendet. Upgrade für mehr Guthaben →",
    "advancedAI": "Erweiterte KI",
    "advancedAIUsage": "<count>3</count><separator> von </separator><total>5</total> kostenlosen Guthaben verwendet",
    "advancedAIEnabled": "Erweiterte KI aktiviert · Nutzung ansehen →",
    "monthlyLimitReached": "Sie haben Ihr monatliches Nutzungslimit erreicht. Aktualisieren Sie, um fortzufahren →",
    "pickDate": "Datum auswählen"
  },
  "mobile": {
    "selectPhotos": "Bitte wählen Sie 1 bis 10 Fotos aus",
    "upload": "Hochladen",
    "select": "Auswählen",
    "selected": "Ausgewählt",
    "allowAccessPhotos": "Zugriff auf Ihre Fotos erlauben",
    "accessPhotosDescription": "Zum Hochladen und Bearbeiten Ihrer Fotos benötigen wir Zugriff auf Ihre Fotobibliothek. Wir greifen nur auf die Fotos zu, die Sie auswählen.",
    "notNow": "Jetzt nicht",
    "allowAccess": "Zugriff erlauben",
    "accessDenied": "Zugriff verweigert",
    "accessDeniedDescription": "Sie haben den Zugriff auf Ihre Fotobibliothek verweigert. Um Bilder hochzuladen, erlauben Sie bitte den Zugriff in den Geräteeinstellungen.",
    "goToSettings": "Zu den Einstellungen gehen",
    "bgColors": "Hintergrundfarben",
    "bgPhotos": "Hintergrundfotos",
    "eraseRestore": "Löschen / Wiederherstellen",
    "blurBG": "Hintergrund verschwimmen"
  },
  "error": {
    "title": "Etwas ist schiefgelaufen",
    "message": "Die Anwendung ist auf einen unerwarteten Fehler gestoßen. Bitte versuchen Sie, die Seite zu aktualisieren.",
    "viewDetails": "Fehlerdetails anzeigen",
    "refresh": "Seite aktualisieren"
  },
  "metadata": {
    "title": "KI-Hintergrund-Entfernungstool - Intelligente Bildhintergrund-Verarbeitung",
    "description": "Verwenden Sie fortschrittliche KI-Technologie, um Bildhintergründe mit einem Klick zu entfernen. Unterstützung für Hintergrundersatz, Farbanpassung, Schattenhinzufügung und andere professionelle Bearbeitungsfunktionen."
  }
}
